#include <Arduino.h>
#include "animations/Breather.h"
#include "bluetooth/BluetoothHelpers.h"
#include "bluetooth/BluetoothA2DP.h"
#include "ui/Buttons.h"
#include "core/DeviceState.h"
#include "bluetooth/SearchingTWS.h"
#include "utils/sleeper.h"

DeviceState currentState = SEARCHING_TWS;  // Start in SEARCHING_TWS state

// Function declarations
void onButtonPress(button_id_t button);
void onLongPress(button_id_t button);
void renderConnectedState();
void renderPlayingState();
void renderPausedState();
void renderStoppedState();

void setup() {
  Serial.begin(115200);
  Serial.println("\n\n=== ESPOD Bluetooth A2DP Source ===\n");

  modifyJoystickForNormalUse();
  initBreather();
  initButtons(onButtonPress, onLongPress);

  // Initialize Bluetooth
  initBluetooth();

  currentState = SEARCHING_TWS;
}

void loop() {
  switch (currentState) {
    case OFF:
      sleepWell();
      break;

    case SEARCHING_TWS:
      renderSearchingTWS();
      break;

    case CONNECTING:
      renderConnectingTWS();
      break;

    case CONNECTED:
      renderConnectedState();
      break;

    case PLAYING:
      renderPlayingState();
      break;

    case PAUSED:
      renderPausedState();
      break;

    case STOPPED:
      renderStoppedState();
      break;

    default:
      Serial.print("Unknown state: ");
      Serial.println(currentState);
      currentState = SEARCHING_TWS; // Reset to a known state
  }

  delay(100);  // Keep the program running
}

// Render connected state
void renderConnectedState() {
  Serial.println("Connected to: " + btA2DP.getConnectedDeviceName());

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(2000);
}

// Render playing state
void renderPlayingState() {
  Serial.println("Playing audio...");

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Render paused state
void renderPausedState() {
  Serial.println("Audio paused");

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Render stopped state
void renderStoppedState() {
  Serial.println("Audio stopped");

  // Check if still connected
  if (!btA2DP.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

// Callback function to handle button press
void onButtonPress(button_id_t button) {
  Serial.print("Button Pressed: ");
  Serial.println(button);

  switch (currentState) {
    case OFF:
      // Only RST long press can wake up from OFF state
      break;

    case SEARCHING_TWS:
      if (button == BTN_UP) {
        // Navigate up in device list
        processBtnClickDuringSearchingTWS('1'); // Map to existing up navigation
      } else if (button == BTN_DOWN) {
        // Navigate down in device list
        processBtnClickDuringSearchingTWS('2'); // Map to existing down navigation
      } else if (button == BTN_CENTER) {
        // Select device
        processBtnClickDuringSearchingTWS('4'); // Map to existing select
      }
      break;

    case CONNECTING:
      // No button actions during connecting
      break;

    case CONNECTED:
      if (button == BTN_CENTER) {
        // Start playing
        btA2DP.play();
        currentState = PLAYING;
      }
      break;

    case PLAYING:
      if (button == BTN_UP) {
        // Volume up
        btA2DP.volumeUp();
      } else if (button == BTN_DOWN) {
        // Volume down
        btA2DP.volumeDown();
      } else if (button == BTN_LEFT) {
        // Previous track
        btA2DP.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        btA2DP.nextTrack();
      } else if (button == BTN_CENTER) {
        // Pause
        btA2DP.pause();
        currentState = PAUSED;
      }
      break;

    case PAUSED:
      if (button == BTN_UP) {
        // Volume up
        btA2DP.volumeUp();
      } else if (button == BTN_DOWN) {
        // Volume down
        btA2DP.volumeDown();
      } else if (button == BTN_LEFT) {
        // Previous track
        btA2DP.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        btA2DP.nextTrack();
      } else if (button == BTN_CENTER) {
        // Resume playing
        btA2DP.play();
        currentState = PLAYING;
      }
      break;

    case STOPPED:
      if (button == BTN_UP) {
        // Volume up
        btA2DP.volumeUp();
      } else if (button == BTN_DOWN) {
        // Volume down
        btA2DP.volumeDown();
      } else if (button == BTN_LEFT) {
        // Previous track
        btA2DP.previousTrack();
      } else if (button == BTN_RIGHT) {
        // Next track
        btA2DP.nextTrack();
      } else if (button == BTN_CENTER) {
        // Start playing
        btA2DP.play();
        currentState = PLAYING;
      }
      break;

    default:
      Serial.println("Unknown state for button press");
  }
}

void onLongPress(button_id_t button) {
  Serial.printf("Long press detected on button: %d\n", button);

  if (button == BTN_RST) {
    if (currentState == OFF) {
      // Wake up from OFF state
      Serial.println("Waking up device...");
      currentState = SEARCHING_TWS;
    } else {
      // Turn off device from any other state
      Serial.println("Turning off device...");
      if (btA2DP.isConnected()) {
        btA2DP.disconnect();
      }
      currentState = OFF;
    }
  }
}
