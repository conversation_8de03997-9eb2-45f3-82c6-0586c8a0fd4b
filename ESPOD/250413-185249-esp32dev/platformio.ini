; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
monitor_speed = 115200
upload_port = /dev/cu.usbserial-0001
monitor_port = /dev/cu.usbserial-0001
upload_speed = 460800

framework = arduino

; Enable Bluetooth Classic and A2DP Source
build_flags =
    -DCONFIG_BT_ENABLED=1
    -DCONFIG_BLUEDROID_ENABLED=1
    -DCONFIG_CLASSIC_BT_ENABLED=1
    -DCONFIG_BT_CLASSIC_ENABLED=1
    -DCONFIG_A2DP_ENABLE=1
    -DCONFIG_A2DP_SOURCE_ENABLE=1
    -DCONFIG_BT_SPP_ENABLED=0

# No external library dependencies needed for ESP-IDF Classic Bluetooth
# lib_deps =
